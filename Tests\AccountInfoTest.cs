using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Services;
using ZeroDateStrat.Utils;

namespace ZeroDateStrat.Tests;

public class AccountInfoTest
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<AccountInfoTest> _logger;

    public AccountInfoTest()
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/zerodtestrat-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        var host = CreateHostBuilder().Build();
        _serviceProvider = host.Services;
        _logger = _serviceProvider.GetRequiredService<ILogger<AccountInfoTest>>();
    }

    private static IHostBuilder CreateHostBuilder() =>
        Host.CreateDefaultBuilder()
            .UseSerilog()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddEnvironmentVariables();
            })
            .ConfigureServices((context, services) =>
            {
                services.AddSingleton<IAlpacaService, AlpacaService>();
                services.AddSingleton<IRiskManager, RiskManager>();
                services.AddLogging(builder =>
                {
                    builder.ClearProviders();
                    builder.AddSerilog();
                });
            });

    public async Task TestAccountInfo()
    {
        Console.WriteLine("=== Account Information Test ===\n");

        try
        {
            var alpacaService = _serviceProvider.GetRequiredService<IAlpacaService>();
            var riskManager = _serviceProvider.GetRequiredService<IRiskManager>();

            // Initialize Alpaca connection
            Console.WriteLine("1. Initializing Alpaca connection...");
            if (!await alpacaService.InitializeAsync())
            {
                Console.WriteLine("   ❌ Failed to initialize Alpaca service");
                return;
            }
            Console.WriteLine("   ✅ Alpaca connection established");

            // Get account information
            Console.WriteLine("\n2. Retrieving account information...");
            var account = await alpacaService.GetAccountAsync();
            if (account == null)
            {
                Console.WriteLine("   ❌ Failed to retrieve account information");
                return;
            }

            Console.WriteLine($"   Account Number: {account.AccountNumber}");
            Console.WriteLine($"   Account Status: {account.Status}");
            Console.WriteLine($"   Equity: {account.Equity:C2}");
            Console.WriteLine($"   Buying Power: {account.BuyingPower:C2}");
            Console.WriteLine($"   Day Trading Buying Power: {account.DayTradingBuyingPower:C2}");
            Console.WriteLine($"   Long Market Value: {account.LongMarketValue:C2}");
            Console.WriteLine($"   Short Market Value: {account.ShortMarketValue:C2}");

            // Test risk calculations
            Console.WriteLine("\n3. Testing risk calculations...");
            var maxRiskPerTrade = await riskManager.GetMaxRiskPerTradeAsync();
            Console.WriteLine($"   Max Risk Per Trade: {maxRiskPerTrade:C2}");

            var accountLimitsOk = await riskManager.CheckAccountLimitsAsync();
            Console.WriteLine($"   Account Limits OK: {accountLimitsOk}");

            var dailyPnL = await riskManager.GetDailyPnLAsync();
            Console.WriteLine($"   Daily P&L: {dailyPnL:C2}");

            // Test VaR calculations
            var varLimit = await riskManager.GetVaRLimitAsync();
            Console.WriteLine($"   VaR Limit: {varLimit:C2}");

            Console.WriteLine("\n=== Account Information Test Completed ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Account info test failed: {ex.Message}");
            _logger.LogError(ex, "Account info test failed");
        }
    }

    public static async Task RunAccountInfoTest()
    {
        var test = new AccountInfoTest();
        await test.TestAccountInfo();
    }
}
