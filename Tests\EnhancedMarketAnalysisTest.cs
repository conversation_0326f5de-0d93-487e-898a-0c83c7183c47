using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Services;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Tests;

public static class EnhancedMarketAnalysisTest
{
    private static IServiceProvider _serviceProvider = null!;
    private static Microsoft.Extensions.Logging.ILogger _logger = null!;

    public static async Task RunEnhancedMarketAnalysisTest()
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/enhanced-market-analysis-test-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        try
        {
            // Setup DI container
            var host = CreateHostBuilder().Build();
            _serviceProvider = host.Services;
            _logger = _serviceProvider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<MarketRegimeAnalyzer>>();

            await TestEnhancedMarketAnalysis();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Enhanced market analysis test failed: {ex.Message}");
            Log.Fatal(ex, "Enhanced market analysis test failed");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static IHostBuilder CreateHostBuilder() =>
        Host.CreateDefaultBuilder()
            .UseSerilog()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddEnvironmentVariables();
            })
            .ConfigureServices((context, services) =>
            {
                // Register services
                services.AddSingleton<IAlpacaService, AlpacaService>();
                services.AddSingleton<IMarketRegimeAnalyzer, MarketRegimeAnalyzer>();
                services.AddSingleton<IOptionsScanner, OptionsScanner>();

                // Add logging
                services.AddLogging(builder =>
                {
                    builder.ClearProviders();
                    builder.AddSerilog();
                });
            });

    public async static Task TestEnhancedMarketAnalysis()
    {
        Console.WriteLine("=== Enhanced Market Analysis Test ===\n");

        try
        {
            var marketAnalyzer = _serviceProvider.GetRequiredService<IMarketRegimeAnalyzer>();
            var optionsScanner = _serviceProvider.GetRequiredService<IOptionsScanner>();

            // Test 1: Volatility Forecasting
            Console.WriteLine("1. Testing Enhanced Volatility Forecasting...");
            var volForecast = await marketAnalyzer.GetVolatilityForecastAsync("SPY", 5);
            
            Console.WriteLine($"   Symbol: {volForecast.Symbol}");
            Console.WriteLine($"   Current Volatility: {volForecast.CurrentVolatility:F2}%");
            Console.WriteLine($"   Forecasted Volatility: {volForecast.ForecastedVolatility:F2}%");
            Console.WriteLine($"   Volatility Trend: {volForecast.VolatilityTrend:F3}");
            Console.WriteLine($"   Confidence: {volForecast.Confidence:P1}");
            Console.WriteLine($"   Model: {volForecast.Model}");
            Console.WriteLine($"   Forecast Path: [{string.Join(", ", volForecast.VolatilityPath.Take(3).Select(v => v.ToString("F1")))}...]");

            // Test 2: Market Microstructure Analysis
            Console.WriteLine("\n2. Testing Market Microstructure Analysis...");
            var microstructure = await marketAnalyzer.GetMarketMicrostructureAsync("SPY");
            
            Console.WriteLine($"   Symbol: {microstructure.Symbol}");
            Console.WriteLine($"   VWAP: {microstructure.VolumeWeightedAveragePrice:F2}");
            Console.WriteLine($"   Bid-Ask Spread: {microstructure.BidAskSpread:P2}");
            Console.WriteLine($"   Order Flow Imbalance: {microstructure.OrderFlowImbalance:F4}");
            Console.WriteLine($"   Liquidity Score: {microstructure.LiquidityScore:F1}");
            Console.WriteLine($"   Momentum Score: {microstructure.MomentumScore:F2}%");
            Console.WriteLine($"   Market Impact: {microstructure.MarketImpact:F6}");
            
            if (microstructure.TechnicalIndicators.Any())
            {
                Console.WriteLine("   Technical Indicators:");
                foreach (var indicator in microstructure.TechnicalIndicators)
                {
                    Console.WriteLine($"     {indicator.Key}: {indicator.Value:F2}");
                }
            }

            // Test 3: Regime Transition Probability
            Console.WriteLine("\n3. Testing Regime Transition Probability...");
            var regimeTransition = await marketAnalyzer.GetRegimeTransitionProbabilityAsync();
            
            Console.WriteLine($"   Current Regime: {regimeTransition.CurrentRegime}");
            Console.WriteLine($"   Days in Current Regime: {regimeTransition.DaysInCurrentRegime}");
            Console.WriteLine($"   Regime Stability: {regimeTransition.RegimeStability:P1}");
            Console.WriteLine($"   Transition Signal: {regimeTransition.TransitionSignal:F3}");
            
            if (regimeTransition.TransitionProbabilities.Any())
            {
                Console.WriteLine("   Transition Probabilities:");
                foreach (var prob in regimeTransition.TransitionProbabilities)
                {
                    Console.WriteLine($"     To {prob.Key}: {prob.Value:P1}");
                }
            }

            // Test 4: Multi-Timeframe Analysis
            Console.WriteLine("\n4. Testing Multi-Timeframe Analysis...");
            var multiTimeframe = await marketAnalyzer.GetMultiTimeframeAnalysisAsync("SPY");
            
            Console.WriteLine($"   Symbol: {multiTimeframe.Symbol}");
            Console.WriteLine($"   Overall Momentum: {multiTimeframe.OverallMomentum:F3}");
            Console.WriteLine($"   Trend Alignment: {multiTimeframe.TrendAlignment:F3}");
            Console.WriteLine($"   Dominant Timeframe: {multiTimeframe.DominantTimeframe}");
            Console.WriteLine($"   Signal Strength: {multiTimeframe.SignalStrength:F3}");
            
            if (multiTimeframe.TimeframeTrends.Any())
            {
                Console.WriteLine("   Timeframe Analysis:");
                foreach (var timeframe in multiTimeframe.TimeframeTrends)
                {
                    Console.WriteLine($"     {timeframe.Key}: {timeframe.Value.Trend} (Strength: {timeframe.Value.Strength:F2}, Momentum: {timeframe.Value.Momentum:F2}%)");
                }
            }

            // Test 5: Market Stress Indicators
            Console.WriteLine("\n5. Testing Market Stress Indicators...");
            var stressIndicators = await marketAnalyzer.GetMarketStressIndicatorsAsync();
            
            Console.WriteLine($"   VIX Level: {stressIndicators.VixLevel:F2}");
            Console.WriteLine($"   Overall Stress Level: {stressIndicators.StressLevel:F1}/100");
            
            if (stressIndicators.StressFactors.Any())
            {
                Console.WriteLine("   Stress Factors:");
                foreach (var factor in stressIndicators.StressFactors)
                {
                    Console.WriteLine($"     - {factor}");
                }
            }

            // Test 6: Volatility Surface
            Console.WriteLine("\n6. Testing Volatility Surface Analysis...");
            var volSurface = await marketAnalyzer.GetVolatilitySurfaceAsync("SPY");
            
            Console.WriteLine($"   Symbol: {volSurface.Symbol}");
            Console.WriteLine($"   ATM Volatility: {volSurface.AtmVolatility:F2}%");
            Console.WriteLine($"   Volatility Skew: {volSurface.VolatilitySkew:F3}");
            Console.WriteLine($"   Term Structure Slope: {volSurface.TermStructureSlope:F3}");

            Console.WriteLine("\n=== Enhanced Market Analysis Test Completed Successfully ===");

        }
        catch (Exception ex)
        {
            Console.WriteLine($"Enhanced market analysis test failed: {ex.Message}");
            _logger.LogError(ex, "Enhanced market analysis test failed");
        }
    }
}
