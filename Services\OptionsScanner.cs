using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IOptionsScanner
{
    Task<List<OptionChain>> ScanForZeroDteOptionsAsync(List<string> symbols);
    Task<List<TradingSignal>> FindTradingOpportunitiesAsync(List<OptionChain> optionChains);
    Task<List<TradingSignal>> FindPutCreditSpreadOpportunitiesAsync(OptionChain chain);
    Task<List<TradingSignal>> FindCallCreditSpreadOpportunitiesAsync(OptionChain chain);
    Task<List<TradingSignal>> FindIronButterflyOpportunitiesAsync(OptionChain chain);

    // Enhanced signal quality methods
    Task<decimal> CalculateSignalQualityScoreAsync(TradingSignal signal, OptionChain chain);
    Task<List<TradingSignal>> RankSignalsByQualityAsync(List<TradingSignal> signals, List<OptionChain> chains);
    Task<SignalValidationResult> ValidateSignalQualityAsync(TradingSignal signal, OptionChain chain);
}

public class OptionsScanner : IOptionsScanner
{
    private readonly ILogger<OptionsScanner> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAlpacaService _alpacaService;
    private readonly IMarketRegimeAnalyzer _marketRegimeAnalyzer;

    public OptionsScanner(
        ILogger<OptionsScanner> logger,
        IConfiguration configuration,
        IAlpacaService alpacaService,
        IMarketRegimeAnalyzer marketRegimeAnalyzer)
    {
        _logger = logger;
        _configuration = configuration;
        _alpacaService = alpacaService;
        _marketRegimeAnalyzer = marketRegimeAnalyzer;
    }

    public async Task<List<OptionChain>> ScanForZeroDteOptionsAsync(List<string> symbols)
    {
        var optionChains = new List<OptionChain>();
        var today = DateTime.Today;

        foreach (var symbol in symbols)
        {
            try
            {
                _logger.LogInformation($"Scanning 0 DTE options for {symbol}");

                var currentPrice = await _alpacaService.GetCurrentPriceAsync(symbol);
                if (currentPrice <= 0)
                {
                    _logger.LogWarning($"Could not get current price for {symbol}");
                    continue;
                }

                var options = await _alpacaService.GetOptionChainAsync(symbol, today);
                if (!options.Any())
                {
                    _logger.LogInformation($"No 0 DTE options found for {symbol}");
                    continue;
                }

                var optionChain = new OptionChain
                {
                    UnderlyingSymbol = symbol,
                    UnderlyingPrice = currentPrice,
                    ExpirationDate = today,
                    LastUpdated = DateTime.UtcNow
                };

                // Separate calls and puts
                optionChain.Calls = options.Where(o => o.OptionType == OptionType.Call).ToList();
                optionChain.Puts = options.Where(o => o.OptionType == OptionType.Put).ToList();

                // Filter for liquid options
                optionChain.Calls = optionChain.Calls.Where(o => o.IsLiquid && o.IsZeroDte).ToList();
                optionChain.Puts = optionChain.Puts.Where(o => o.IsLiquid && o.IsZeroDte).ToList();

                if (optionChain.Calls.Any() || optionChain.Puts.Any())
                {
                    optionChains.Add(optionChain);
                    _logger.LogInformation($"Found {optionChain.Calls.Count} calls and {optionChain.Puts.Count} puts for {symbol}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error scanning options for {symbol}");
            }
        }

        return optionChains;
    }

    public async Task<List<TradingSignal>> FindTradingOpportunitiesAsync(List<OptionChain> optionChains)
    {
        var signals = new List<TradingSignal>();
        var recommendedStrategies = await _marketRegimeAnalyzer.GetRecommendedStrategiesAsync();

        if (!recommendedStrategies.Any())
        {
            _logger.LogInformation("No strategies recommended for current market regime");
            return signals;
        }

        foreach (var chain in optionChains)
        {
            try
            {
                // Only look for strategies recommended by market regime analyzer
                foreach (var strategy in recommendedStrategies)
                {
                    switch (strategy)
                    {
                        case "PutCreditSpread":
                            var putCreditSignals = await FindPutCreditSpreadOpportunitiesAsync(chain);
                            signals.AddRange(putCreditSignals);
                            break;

                        case "CallCreditSpread":
                            var callCreditSignals = await FindCallCreditSpreadOpportunitiesAsync(chain);
                            signals.AddRange(callCreditSignals);
                            break;

                        case "IronButterfly":
                            var butterflySignals = await FindIronButterflyOpportunitiesAsync(chain);
                            signals.AddRange(butterflySignals);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error finding opportunities for {chain.UnderlyingSymbol}");
            }
        }

        // Sort by strategy priority, then confidence
        return signals.Where(s => s.IsValid)
                     .OrderBy(s => GetStrategyPriority(s.Strategy))
                     .ThenByDescending(s => s.Confidence)
                     .ThenByDescending(s => s.RiskRewardRatio)
                     .ToList();
    }

    public async Task<List<TradingSignal>> FindPutCreditSpreadOpportunitiesAsync(OptionChain chain)
    {
        var signals = new List<TradingSignal>();

        if (!_configuration.GetValue<bool>("Strategies:PutCreditSpread:Enabled", true))
            return signals;

        try
        {
            var maxSpreadWidth = _configuration.GetValue<decimal>("Strategies:PutCreditSpread:MaxSpreadWidth", 10);
            var minDelta = _configuration.GetValue<decimal>("Strategies:PutCreditSpread:MinDelta", 0.05m);
            var maxDelta = _configuration.GetValue<decimal>("Strategies:PutCreditSpread:MaxDelta", 0.15m);
            var minPremium = _configuration.GetValue<decimal>("Strategies:PutCreditSpread:MinPremium", 0.10m);

            // Find puts with target delta range
            var suitablePuts = chain.Puts.Where(p =>
                Math.Abs(p.Delta) >= minDelta &&
                Math.Abs(p.Delta) <= maxDelta &&
                p.IsLiquid &&
                p.StrikePrice < chain.UnderlyingPrice &&
                p.MidPrice >= minPremium).ToList();

            foreach (var shortPut in suitablePuts)
            {
                var longPutStrike = shortPut.StrikePrice - maxSpreadWidth;
                var longPut = chain.GetPutByStrike(longPutStrike);

                if (longPut != null && longPut.IsLiquid)
                {
                    var credit = shortPut.MidPrice - longPut.MidPrice;
                    var maxLoss = maxSpreadWidth - credit;
                    var profitTarget = _configuration.GetValue<decimal>("Strategies:PutCreditSpread:ProfitTarget", 0.5m);

                    if (credit >= minPremium && maxLoss > 0)
                    {
                        var signal = new TradingSignal
                        {
                            Strategy = "PutCreditSpread",
                            UnderlyingSymbol = chain.UnderlyingSymbol,
                            Type = SignalType.PutSpread,
                            ExpirationDate = chain.ExpirationDate,
                            ExpectedProfit = credit * profitTarget,
                            MaxLoss = maxLoss,
                            Confidence = CalculatePutCreditSpreadConfidence(shortPut, longPut, credit, chain),
                            Reason = $"Put Credit Spread: {shortPut.StrikePrice}/{longPut.StrikePrice}, Credit={credit:C2}, Delta={shortPut.Delta:F2}",
                            Legs = new List<OptionLeg>
                            {
                                new() { Symbol = shortPut.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Put, StrikePrice = shortPut.StrikePrice, Side = OrderSide.Sell, Quantity = 1, Price = shortPut.MidPrice, Delta = shortPut.Delta },
                                new() { Symbol = longPut.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Put, StrikePrice = longPut.StrikePrice, Side = OrderSide.Buy, Quantity = 1, Price = longPut.MidPrice, Delta = longPut.Delta }
                            }
                        };

                        signals.Add(signal);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error finding put credit spread opportunities for {chain.UnderlyingSymbol}");
        }

        return signals.Take(2).ToList(); // Limit to top 2 opportunities
    }

    public async Task<List<TradingSignal>> FindCallCreditSpreadOpportunitiesAsync(OptionChain chain)
    {
        var signals = new List<TradingSignal>();

        if (!_configuration.GetValue<bool>("Strategies:CallCreditSpread:Enabled", true))
            return signals;

        try
        {
            var maxSpreadWidth = _configuration.GetValue<decimal>("Strategies:CallCreditSpread:MaxSpreadWidth", 10);
            var minDelta = _configuration.GetValue<decimal>("Strategies:CallCreditSpread:MinDelta", 0.05m);
            var maxDelta = _configuration.GetValue<decimal>("Strategies:CallCreditSpread:MaxDelta", 0.15m);
            var minPremium = _configuration.GetValue<decimal>("Strategies:CallCreditSpread:MinPremium", 0.10m);

            // Find calls with target delta range
            var suitableCalls = chain.Calls.Where(c =>
                c.Delta >= minDelta &&
                c.Delta <= maxDelta &&
                c.IsLiquid &&
                c.StrikePrice > chain.UnderlyingPrice &&
                c.MidPrice >= minPremium).ToList();

            foreach (var shortCall in suitableCalls)
            {
                var longCallStrike = shortCall.StrikePrice + maxSpreadWidth;
                var longCall = chain.GetCallByStrike(longCallStrike);

                if (longCall != null && longCall.IsLiquid)
                {
                    var credit = shortCall.MidPrice - longCall.MidPrice;
                    var maxLoss = maxSpreadWidth - credit;
                    var profitTarget = _configuration.GetValue<decimal>("Strategies:CallCreditSpread:ProfitTarget", 0.5m);

                    if (credit >= minPremium && maxLoss > 0)
                    {
                        var signal = new TradingSignal
                        {
                            Strategy = "CallCreditSpread",
                            UnderlyingSymbol = chain.UnderlyingSymbol,
                            Type = SignalType.CallSpread,
                            ExpirationDate = chain.ExpirationDate,
                            ExpectedProfit = credit * profitTarget,
                            MaxLoss = maxLoss,
                            Confidence = CalculateCallCreditSpreadConfidence(shortCall, longCall, credit, chain),
                            Reason = $"Call Credit Spread: {shortCall.StrikePrice}/{longCall.StrikePrice}, Credit={credit:C2}, Delta={shortCall.Delta:F2}",
                            Legs = new List<OptionLeg>
                            {
                                new() { Symbol = shortCall.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Call, StrikePrice = shortCall.StrikePrice, Side = OrderSide.Sell, Quantity = 1, Price = shortCall.MidPrice, Delta = shortCall.Delta },
                                new() { Symbol = longCall.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Call, StrikePrice = longCall.StrikePrice, Side = OrderSide.Buy, Quantity = 1, Price = longCall.MidPrice, Delta = longCall.Delta }
                            }
                        };

                        signals.Add(signal);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error finding call credit spread opportunities for {chain.UnderlyingSymbol}");
        }

        return signals.Take(2).ToList();
    }

    public async Task<List<TradingSignal>> FindIronButterflyOpportunitiesAsync(OptionChain chain)
    {
        var signals = new List<TradingSignal>();

        if (!_configuration.GetValue<bool>("Strategies:IronButterfly:Enabled", true))
            return signals;

        try
        {
            var wingWidth = _configuration.GetValue<decimal>("Strategies:IronButterfly:WingWidth", 25);
            var minPremium = _configuration.GetValue<decimal>("Strategies:IronButterfly:MinPremium", 0.15m);
            var atmRange = _configuration.GetValue<decimal>("Strategies:IronButterfly:ATMRange", 0.02m);

            // Find ATM strike (round to nearest $5 for SPX, $1 for SPY)
            var strikeIncrement = chain.UnderlyingSymbol == "SPX" ? 5m : 1m;
            var atmStrike = Math.Round(chain.UnderlyingPrice / strikeIncrement) * strikeIncrement;

            // Iron Butterfly: Sell ATM straddle, buy wings
            var atmCall = chain.GetCallByStrike(atmStrike);
            var atmPut = chain.GetPutByStrike(atmStrike);
            var longCall = chain.GetCallByStrike(atmStrike + wingWidth);
            var longPut = chain.GetPutByStrike(atmStrike - wingWidth);

            if (atmCall != null && atmPut != null && longCall != null && longPut != null &&
                atmCall.IsLiquid && atmPut.IsLiquid && longCall.IsLiquid && longPut.IsLiquid)
            {
                var credit = (atmCall.MidPrice + atmPut.MidPrice) - (longCall.MidPrice + longPut.MidPrice);
                var maxLoss = wingWidth - credit;
                var profitTarget = _configuration.GetValue<decimal>("Strategies:IronButterfly:ProfitTarget", 0.5m);

                if (credit >= minPremium && maxLoss > 0)
                {
                    var signal = new TradingSignal
                    {
                        Strategy = "IronButterfly",
                        UnderlyingSymbol = chain.UnderlyingSymbol,
                        Type = SignalType.IronButterfly,
                        ExpirationDate = chain.ExpirationDate,
                        ExpectedProfit = credit * profitTarget,
                        MaxLoss = maxLoss,
                        Confidence = CalculateIronButterflyConfidence(atmCall, atmPut, credit, chain),
                        Reason = $"Iron Butterfly: ATM={atmStrike}, Credit={credit:C2}, Wings=±{wingWidth}",
                        Legs = new List<OptionLeg>
                        {
                            new() { Symbol = atmCall.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Call, StrikePrice = atmStrike, Side = OrderSide.Sell, Quantity = 1, Price = atmCall.MidPrice },
                            new() { Symbol = atmPut.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Put, StrikePrice = atmStrike, Side = OrderSide.Sell, Quantity = 1, Price = atmPut.MidPrice },
                            new() { Symbol = longCall.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Call, StrikePrice = atmStrike + wingWidth, Side = OrderSide.Buy, Quantity = 1, Price = longCall.MidPrice },
                            new() { Symbol = longPut.Symbol, UnderlyingSymbol = chain.UnderlyingSymbol, OptionType = OptionType.Put, StrikePrice = atmStrike - wingWidth, Side = OrderSide.Buy, Quantity = 1, Price = longPut.MidPrice }
                        }
                    };

                    signals.Add(signal);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error finding iron butterfly opportunities for {chain.UnderlyingSymbol}");
        }

        return signals.Take(1).ToList(); // Only one iron butterfly per underlying
    }

    private int GetStrategyPriority(string strategy)
    {
        return strategy switch
        {
            "PutCreditSpread" => 1,
            "CallCreditSpread" => 2,
            "IronButterfly" => 3,
            "IronCondor" => 4,
            _ => 99
        };
    }

    private decimal CalculatePutCreditSpreadConfidence(OptionContract shortPut, OptionContract longPut, decimal credit, OptionChain chain)
    {
        try
        {
            // Risk/reward ratio (higher is better)
            var maxLoss = Math.Abs(shortPut.StrikePrice - longPut.StrikePrice) - credit;
            var riskReward = maxLoss > 0 ? credit / maxLoss : 0;

            // Liquidity score (higher volume = better)
            var liquidityScore = Math.Min(1.0m, (shortPut.Volume + longPut.Volume) / 50);

            // Delta appropriateness (closer to target delta = better)
            var targetDelta = 0.10m;
            var deltaScore = 1 - Math.Abs(Math.Abs(shortPut.Delta) - targetDelta) / targetDelta;

            // Distance from current price (further OTM = safer)
            var distanceScore = Math.Min(1.0m, (chain.UnderlyingPrice - shortPut.StrikePrice) / chain.UnderlyingPrice * 10);

            // Time decay benefit (always high for 0 DTE)
            var timeDecayScore = 0.9m;

            var confidence = (riskReward * 0.25m) + (liquidityScore * 0.20m) + (deltaScore * 0.25m) +
                           (distanceScore * 0.15m) + (timeDecayScore * 0.15m);

            return Math.Min(0.95m, Math.Max(0.1m, confidence));
        }
        catch
        {
            return 0.5m; // Default confidence
        }
    }

    private decimal CalculateCallCreditSpreadConfidence(OptionContract shortCall, OptionContract longCall, decimal credit, OptionChain chain)
    {
        try
        {
            var maxLoss = Math.Abs(longCall.StrikePrice - shortCall.StrikePrice) - credit;
            var riskReward = maxLoss > 0 ? credit / maxLoss : 0;
            var liquidityScore = Math.Min(1.0m, (shortCall.Volume + longCall.Volume) / 50);
            var targetDelta = 0.10m;
            var deltaScore = 1 - Math.Abs(shortCall.Delta - targetDelta) / targetDelta;
            var distanceScore = Math.Min(1.0m, (shortCall.StrikePrice - chain.UnderlyingPrice) / chain.UnderlyingPrice * 10);
            var timeDecayScore = 0.9m;

            var confidence = (riskReward * 0.25m) + (liquidityScore * 0.20m) + (deltaScore * 0.25m) +
                           (distanceScore * 0.15m) + (timeDecayScore * 0.15m);

            return Math.Min(0.95m, Math.Max(0.1m, confidence));
        }
        catch
        {
            return 0.5m;
        }
    }

    private decimal CalculateIronButterflyConfidence(OptionContract atmCall, OptionContract atmPut, decimal credit, OptionChain chain)
    {
        try
        {
            // Credit quality (higher credit = better, but not too high)
            var creditScore = credit >= 0.50m && credit <= 1.50m ? 0.9m : 0.5m;

            // ATM liquidity (critical for iron butterfly)
            var liquidityScore = Math.Min(1.0m, (atmCall.Volume + atmPut.Volume) / 100);

            // Volatility appropriateness (lower IV = better for selling premium)
            var ivScore = atmCall.ImpliedVolatility > 0 ? Math.Max(0.3m, 1 - (atmCall.ImpliedVolatility / 0.5m)) : 0.7m;

            // Time decay benefit
            var timeDecayScore = 0.95m; // Very high for 0 DTE

            // Market neutrality benefit (works best in range-bound markets)
            var neutralityScore = 0.8m;

            var confidence = (creditScore * 0.25m) + (liquidityScore * 0.25m) + (ivScore * 0.20m) +
                           (timeDecayScore * 0.15m) + (neutralityScore * 0.15m);

            return Math.Min(0.95m, Math.Max(0.1m, confidence));
        }
        catch
        {
            return 0.6m;
        }
    }

    // Enhanced Signal Quality Implementation
    public async Task<decimal> CalculateSignalQualityScoreAsync(TradingSignal signal, OptionChain chain)
    {
        try
        {
            _logger.LogDebug($"Calculating signal quality score for {signal.Strategy} on {signal.UnderlyingSymbol}");

            var qualityFactors = new Dictionary<string, decimal>();

            // Factor 1: Liquidity Score (25% weight)
            var liquidityScore = CalculateLiquidityScore(signal, chain);
            qualityFactors["Liquidity"] = liquidityScore * 0.25m;

            // Factor 2: Risk-Reward Ratio (20% weight)
            var riskRewardScore = CalculateRiskRewardScore(signal);
            qualityFactors["RiskReward"] = riskRewardScore * 0.20m;

            // Factor 3: Market Regime Alignment (20% weight)
            var regimeScore = await CalculateRegimeAlignmentScore(signal);
            qualityFactors["RegimeAlignment"] = regimeScore * 0.20m;

            // Factor 4: Technical Setup Quality (15% weight)
            var technicalScore = await CalculateTechnicalSetupScore(signal, chain);
            qualityFactors["TechnicalSetup"] = technicalScore * 0.15m;

            // Factor 5: Volatility Environment (10% weight)
            var volatilityScore = await CalculateVolatilityScore(signal, chain);
            qualityFactors["Volatility"] = volatilityScore * 0.10m;

            // Factor 6: Time Decay Favorability (10% weight)
            var timeDecayScore = CalculateTimeDecayScore(signal);
            qualityFactors["TimeDecay"] = timeDecayScore * 0.10m;

            var totalScore = qualityFactors.Values.Sum();

            // Store quality breakdown in signal parameters
            signal.Parameters["QualityBreakdown"] = qualityFactors;
            signal.Parameters["QualityScore"] = totalScore;

            _logger.LogDebug($"Signal quality score for {signal.Id}: {totalScore:F3} (Liquidity: {liquidityScore:F2}, RiskReward: {riskRewardScore:F2}, Regime: {regimeScore:F2})");

            return Math.Max(0, Math.Min(1, totalScore));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating signal quality score for {signal.Id}");
            return 0.5m; // Default neutral score
        }
    }

    public async Task<List<TradingSignal>> RankSignalsByQualityAsync(List<TradingSignal> signals, List<OptionChain> chains)
    {
        try
        {
            _logger.LogInformation($"Ranking {signals.Count} signals by quality");

            var scoredSignals = new List<(TradingSignal Signal, decimal QualityScore)>();

            foreach (var signal in signals)
            {
                var chain = chains.FirstOrDefault(c => c.UnderlyingSymbol == signal.UnderlyingSymbol);
                if (chain != null)
                {
                    var qualityScore = await CalculateSignalQualityScoreAsync(signal, chain);
                    scoredSignals.Add((signal, qualityScore));
                }
                else
                {
                    scoredSignals.Add((signal, 0.3m)); // Low score if no chain data
                }
            }

            // Sort by quality score (descending) and return signals
            var rankedSignals = scoredSignals
                .OrderByDescending(s => s.QualityScore)
                .ThenByDescending(s => s.Signal.Confidence)
                .ThenByDescending(s => s.Signal.RiskRewardRatio)
                .Select(s => s.Signal)
                .ToList();

            _logger.LogInformation($"Signal ranking completed. Top signal: {rankedSignals.FirstOrDefault()?.Strategy} with quality score: {scoredSignals.FirstOrDefault().QualityScore:F3}");

            return rankedSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ranking signals by quality");
            return signals; // Return original list on error
        }
    }

    public async Task<SignalValidationResult> ValidateSignalQualityAsync(TradingSignal signal, OptionChain chain)
    {
        try
        {
            var validation = new SignalValidationResult
            {
                SignalId = signal.Id,
                IsValid = true,
                ValidationTimestamp = DateTime.UtcNow
            };

            var issues = new List<string>();
            var warnings = new List<string>();

            // Validate liquidity
            var liquidityScore = CalculateLiquidityScore(signal, chain);
            if (liquidityScore < 0.3m)
            {
                issues.Add($"Poor liquidity score: {liquidityScore:F2}");
                validation.IsValid = false;
            }
            else if (liquidityScore < 0.6m)
            {
                warnings.Add($"Moderate liquidity concern: {liquidityScore:F2}");
            }

            // Validate risk-reward
            if (signal.RiskRewardRatio < 0.1m)
            {
                issues.Add($"Poor risk-reward ratio: {signal.RiskRewardRatio:F2}");
                validation.IsValid = false;
            }

            // Validate market regime alignment
            var regimeScore = await CalculateRegimeAlignmentScore(signal);
            if (regimeScore < 0.4m)
            {
                warnings.Add($"Poor market regime alignment: {regimeScore:F2}");
            }

            // Validate option Greeks
            foreach (var leg in signal.Legs)
            {
                if (Math.Abs(leg.Delta) > 0.3m && signal.Strategy.Contains("Credit"))
                {
                    warnings.Add($"High delta exposure in credit strategy: {leg.Delta:F3}");
                }
            }

            // Calculate overall quality score
            validation.QualityScore = await CalculateSignalQualityScoreAsync(signal, chain);
            validation.Issues = issues;
            validation.Warnings = warnings;

            if (validation.QualityScore < 0.3m)
            {
                validation.IsValid = false;
                issues.Add($"Overall quality score too low: {validation.QualityScore:F3}");
            }

            _logger.LogDebug($"Signal validation for {signal.Id}: Valid={validation.IsValid}, Quality={validation.QualityScore:F3}, Issues={issues.Count}, Warnings={warnings.Count}");

            return validation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error validating signal quality for {signal.Id}");
            return new SignalValidationResult
            {
                SignalId = signal.Id,
                IsValid = false,
                Issues = new List<string> { "Validation error occurred" },
                ValidationTimestamp = DateTime.UtcNow
            };
        }
    }

    private decimal CalculateLiquidityScore(TradingSignal signal, OptionChain chain)
    {
        try
        {
            var liquidityScores = new List<decimal>();

            foreach (var leg in signal.Legs)
            {
                var option = leg.OptionType == OptionType.Call
                    ? chain.GetCallByStrike(leg.StrikePrice)
                    : chain.GetPutByStrike(leg.StrikePrice);

                if (option != null)
                {
                    // Score based on volume, open interest, and spread
                    var volumeScore = Math.Min(1m, option.Volume / 100m); // Normalize to 100 volume
                    var oiScore = Math.Min(1m, option.OpenInterest / 500m); // Normalize to 500 OI
                    var spreadScore = option.SpreadPercentage < 5 ? 1m :
                                    option.SpreadPercentage < 10 ? 0.7m :
                                    option.SpreadPercentage < 20 ? 0.4m : 0.1m;

                    var legLiquidity = (volumeScore * 0.4m) + (oiScore * 0.4m) + (spreadScore * 0.2m);
                    liquidityScores.Add(legLiquidity);
                }
                else
                {
                    liquidityScores.Add(0.1m); // Very low score for missing option
                }
            }

            return liquidityScores.Any() ? liquidityScores.Average() : 0.1m;
        }
        catch
        {
            return 0.3m; // Default moderate score on error
        }
    }

    private decimal CalculateRiskRewardScore(TradingSignal signal)
    {
        try
        {
            // Score based on risk-reward ratio with 0 DTE adjustments
            var ratio = signal.RiskRewardRatio;

            if (ratio >= 0.3m) return 1.0m;      // Excellent
            if (ratio >= 0.2m) return 0.8m;      // Good
            if (ratio >= 0.15m) return 0.6m;     // Acceptable
            if (ratio >= 0.1m) return 0.4m;      // Poor
            return 0.1m;                          // Very poor
        }
        catch
        {
            return 0.3m;
        }
    }

    private async Task<decimal> CalculateRegimeAlignmentScore(TradingSignal signal)
    {
        try
        {
            var currentRegime = await _marketRegimeAnalyzer.GetCurrentRegimeAsync();
            var recommendedStrategies = await _marketRegimeAnalyzer.GetRecommendedStrategiesAsync();

            // Check if signal strategy is recommended for current regime
            if (recommendedStrategies.Contains(signal.Strategy))
            {
                // Higher score for better regime alignment
                return currentRegime.VolatilityRegime switch
                {
                    VolatilityRegime.Low => signal.Strategy == "PutCreditSpread" ? 0.9m : 0.8m,
                    VolatilityRegime.Medium => 0.7m,
                    VolatilityRegime.High => 0.3m, // Generally avoid trading in high vol
                    _ => 0.5m
                };
            }

            return 0.2m; // Low score if strategy not recommended
        }
        catch
        {
            return 0.5m;
        }
    }

    private async Task<decimal> CalculateTechnicalSetupScore(TradingSignal signal, OptionChain chain)
    {
        try
        {
            var microstructure = await _marketRegimeAnalyzer.GetMarketMicrostructureAsync(signal.UnderlyingSymbol);
            var multiTimeframe = await _marketRegimeAnalyzer.GetMultiTimeframeAnalysisAsync(signal.UnderlyingSymbol);

            var scores = new List<decimal>();

            // Momentum alignment score
            var momentumScore = Math.Max(0, Math.Min(1, (microstructure.MomentumScore + 50) / 100)); // Normalize -50 to +50 range
            scores.Add(momentumScore);

            // Liquidity score from microstructure
            var liquidityScore = microstructure.LiquidityScore / 100m;
            scores.Add(liquidityScore);

            // Multi-timeframe alignment
            var alignmentScore = multiTimeframe.TrendAlignment;
            scores.Add(alignmentScore);

            // Technical indicators
            if (microstructure.TechnicalIndicators.ContainsKey("RSI"))
            {
                var rsi = microstructure.TechnicalIndicators["RSI"];
                var rsiScore = signal.Strategy.Contains("Credit") ?
                    (rsi > 30 && rsi < 70 ? 0.8m : 0.4m) : // Credit spreads prefer neutral RSI
                    (rsi < 30 || rsi > 70 ? 0.8m : 0.4m);   // Other strategies prefer extreme RSI
                scores.Add(rsiScore);
            }

            return scores.Any() ? scores.Average() : 0.5m;
        }
        catch
        {
            return 0.5m;
        }
    }

    private async Task<decimal> CalculateVolatilityScore(TradingSignal signal, OptionChain chain)
    {
        try
        {
            var forecast = await _marketRegimeAnalyzer.GetVolatilityForecastAsync(signal.UnderlyingSymbol);

            // For credit strategies, lower volatility is better
            if (signal.Strategy.Contains("Credit"))
            {
                if (forecast.ForecastedVolatility < 15) return 0.9m;      // Very low vol - excellent
                if (forecast.ForecastedVolatility < 20) return 0.8m;      // Low vol - good
                if (forecast.ForecastedVolatility < 25) return 0.6m;      // Medium vol - acceptable
                if (forecast.ForecastedVolatility < 30) return 0.4m;      // High vol - poor
                return 0.2m;                                               // Very high vol - very poor
            }

            // For other strategies, moderate volatility might be preferred
            if (forecast.ForecastedVolatility >= 15 && forecast.ForecastedVolatility <= 25)
                return 0.8m;

            return 0.5m; // Default moderate score
        }
        catch
        {
            return 0.5m;
        }
    }

    private decimal CalculateTimeDecayScore(TradingSignal signal)
    {
        try
        {
            // For 0 DTE options, time decay is always favorable for sellers
            if (signal.Strategy.Contains("Credit") || signal.Strategy.Contains("Butterfly"))
            {
                return 0.95m; // Very high score for premium selling strategies
            }

            // For buying strategies, time decay is unfavorable
            return 0.3m;
        }
        catch
        {
            return 0.5m;
        }
    }
}
