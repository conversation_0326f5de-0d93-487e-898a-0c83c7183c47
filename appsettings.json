{"Alpaca": {"ApiKey": "AKR6SLIKSB0NCBL2CNLB", "SecretKey": "mgRw02d5XNabcUgopVmb22fDoCEVLsjs7QswywJz", "BaseUrl": "https://api.alpaca.markets", "DataUrl": "https://data.alpaca.markets"}, "Trading": {"PrimarySymbol": "SPX", "BackupSymbol": "SPY", "MaxPositionSize": 10000, "MaxDailyLoss": 500, "RiskPerTrade": 0.02, "MaxPositionsPerDay": 5, "MinDaysToExpiration": 0, "MaxDaysToExpiration": 0, "EntryTimeStart": "09:45:00", "EntryTimeEnd": "10:30:00", "ManagementTime": "14:00:00", "ForceCloseTime": "15:45:00", "TradingEndTime": "16:00:00", "MinAccountEquity": 2000, "MinBuyingPower": 1000, "ProfitTargetPercent": 0.5, "StopLossPercent": 2.0, "RiskRewardThreshold": 0.15}, "MarketRegime": {"VixLowThreshold": 18, "VixHighThreshold": 25, "TrendLookbackPeriods": 20, "VolatilityLookbackPeriods": 14, "VolatilityCalculationDays": 30, "VolatilityForecastLookback": 60, "ATRPeriods": 14, "RSIPeriods": 14, "BollingerBandPeriods": 20, "BollingerBandStdDev": 2.0, "GarchAlpha": 0.1, "GarchBeta": 0.85, "GarchOmega": 1e-05, "MicrostructureLookbackHours": 6, "MultiTimeframeEnabled": true, "RegimeTransitionSensitivity": 0.7}, "Risk": {"MaxDrawdown": 0.08, "VaRLimit": 0.03, "MaxConcentration": 0.6, "MaxCorrelatedExposure": 0.7, "PortfolioHeatLimit": 0.75, "MaxDailyTrades": 8, "MaxOpenPositions": 12, "StressTestMultiplier": 1.5, "RiskRewardMinimum": 0.15, "MaxPositionsPerSymbol": 4, "ConcentrationWarningLevel": 0.5}, "Strategies": {"PutCreditSpread": {"Enabled": true, "Priority": 1, "MinDelta": 0.05, "MaxDelta": 0.15, "MinPremium": 0.1, "MaxSpreadWidth": 10, "MinDaysToExpiration": 0, "MaxDaysToExpiration": 0, "ProfitTarget": 0.5, "StopLoss": 2.0}, "IronButterfly": {"Enabled": true, "Priority": 2, "ATMRange": 0.02, "WingWidth": 25, "MinPremium": 0.15, "ProfitTarget": 0.5, "StopLoss": 2.0}, "CallCreditSpread": {"Enabled": true, "Priority": 3, "MinDelta": 0.05, "MaxDelta": 0.15, "MinPremium": 0.1, "MaxSpreadWidth": 10, "ProfitTarget": 0.5, "StopLoss": 2.0}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "ZeroDateStrat": "Debug"}}}