using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Services;
using ZeroDateStrat.Strategies;
using ZeroDateStrat.Utils;

namespace ZeroDateStrat;

class Program
{
    static async Task Main(string[] args)
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/zerodtestrat-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        try
        {
            // Check if running in test mode
            if (args.Length > 0 && args[0].ToLower() == "test")
            {
                Log.Information("Running in test mode");
                ZeroDateStrat.Tests.BasicTests.RunAllTests();
                return;
            }

            // Check if running integration test
            if (args.Length > 0 && args[0].ToLower() == "integration")
            {
                Log.Information("Running integration test mode");
                await ZeroDateStrat.Tests.OptionsDataIntegrationTest.RunIntegrationTest();
                return;
            }

            // Check if running backtesting test
            if (args.Length > 0 && args[0].ToLower() == "backtest")
            {
                Log.Information("Running backtesting framework test mode");
                await ZeroDateStrat.Tests.BacktestingFrameworkTest.RunBacktestingFrameworkTest();
                return;
            }

            // Check if running debug test
            if (args.Length > 0 && args[0].ToLower() == "debug")
            {
                Log.Information("Running debug test mode");
                await ZeroDateStrat.Tests.DebugBacktestTest.RunDebugTest();
                return;
            }

            // Check if running exit analysis test
            if (args.Length > 0 && args[0].ToLower() == "exit")
            {
                Log.Information("Running exit analysis test mode");
                await ZeroDateStrat.Tests.TradeExitAnalysisTest.RunExitAnalysisTest();
                return;
            }

            // Check if running enhanced risk management test
            if (args.Length > 0 && args[0].ToLower() == "risk")
            {
                Log.Information("Running enhanced risk management test mode");
                await ZeroDateStrat.Tests.EnhancedRiskManagementTest.RunEnhancedRiskManagementTest();
                return;
            }

            // Check if running account info test
            if (args.Length > 0 && args[0].ToLower() == "account")
            {
                Log.Information("Running account info test mode");
                await ZeroDateStrat.Tests.AccountInfoTest.RunAccountInfoTest();
                return;
            }

            // Check if running enhanced market analysis test
            if (args.Length > 0 && args[0].ToLower() == "market")
            {
                Log.Information("Running enhanced market analysis test mode");
                await ZeroDateStrat.Tests.EnhancedMarketAnalysisTest.RunEnhancedMarketAnalysisTest();
                return;
            }

            Log.Information("Starting Zero DTE Trading Application");

            var host = CreateHostBuilder(args).Build();

            // Initialize services
            var alpacaService = host.Services.GetRequiredService<IAlpacaService>();
            var strategy = host.Services.GetRequiredService<IZeroDteStrategy>();
            var logger = host.Services.GetRequiredService<ILogger<Program>>();

            // Initialize Alpaca connection
            logger.LogInformation("Initializing Alpaca connection...");
            if (!await alpacaService.InitializeAsync())
            {
                logger.LogError("Failed to initialize Alpaca service");
                return;
            }

            logger.LogInformation("Alpaca connection established successfully");

            // Start the trading loop
            await RunTradingLoop(strategy, logger);
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Application terminated unexpectedly");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddEnvironmentVariables();
                config.AddCommandLine(args);
            })
            .ConfigureServices((context, services) =>
            {
                // Register services
                services.AddSingleton<IAlpacaService, AlpacaService>();
                services.AddSingleton<IMarketRegimeAnalyzer, MarketRegimeAnalyzer>();
                services.AddSingleton<IOptionsScanner, OptionsScanner>();
                services.AddSingleton<IRiskManager, RiskManager>();
                services.AddSingleton<IZeroDteStrategy, ZeroDteStrategy>();

                // Backtesting Framework Services
                services.AddSingleton<IHistoricalDataService, HistoricalDataService>();
                services.AddSingleton<IBacktestingEngine, BacktestingEngine>();
                services.AddSingleton<IPerformanceAnalytics, PerformanceAnalytics>();

                // Add logging
                services.AddLogging(builder =>
                {
                    builder.ClearProviders();
                    builder.AddSerilog();
                });
            });

    static async Task RunTradingLoop(IZeroDteStrategy strategy, ILogger<Program> logger)
    {
        logger.LogInformation("Starting trading loop...");

        var cancellationTokenSource = new CancellationTokenSource();
        
        // Handle Ctrl+C gracefully
        Console.CancelKeyPress += (sender, e) =>
        {
            e.Cancel = true;
            cancellationTokenSource.Cancel();
            logger.LogInformation("Shutdown requested...");
        };

        try
        {
            while (!cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    // Check if we should trade
                    if (await strategy.ShouldTrade())
                    {
                        logger.LogInformation("Scanning for trading opportunities...");

                        // Generate trading signals
                        var signals = await strategy.GenerateSignalsAsync();
                        
                        if (signals.Any())
                        {
                            logger.LogInformation($"Found {signals.Count} trading signals");

                            // Execute the best signals (limit to 3 per cycle)
                            var signalsToExecute = signals.Take(3);
                            
                            foreach (var signal in signalsToExecute)
                            {
                                logger.LogInformation($"Executing signal: {signal.Strategy} for {signal.UnderlyingSymbol}");
                                logger.LogInformation($"  Expected Profit: {signal.ExpectedProfit:C2}");
                                logger.LogInformation($"  Max Loss: {signal.MaxLoss:C2}");
                                logger.LogInformation($"  Risk/Reward: {signal.RiskRewardRatio:F2}");
                                logger.LogInformation($"  Confidence: {signal.Confidence:P1}");

                                var success = await strategy.ExecuteSignalAsync(signal);
                                if (success)
                                {
                                    logger.LogInformation($"Successfully executed signal {signal.Id}");
                                }
                                else
                                {
                                    logger.LogWarning($"Failed to execute signal {signal.Id}");
                                }

                                // Small delay between executions
                                await Task.Delay(1000, cancellationTokenSource.Token);
                            }
                        }
                        else
                        {
                            logger.LogInformation("No trading opportunities found");
                        }

                        // Manage existing positions
                        await strategy.ManagePositionsAsync();
                    }
                    else
                    {
                        logger.LogDebug("Not trading at this time");
                    }

                    // Wait before next scan (30 seconds)
                    await Task.Delay(30000, cancellationTokenSource.Token);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error in trading loop");
                    await Task.Delay(5000, cancellationTokenSource.Token); // Wait 5 seconds before retrying
                }
            }
        }
        catch (OperationCanceledException)
        {
            logger.LogInformation("Trading loop cancelled");
        }

        logger.LogInformation("Trading loop stopped");
    }
}
